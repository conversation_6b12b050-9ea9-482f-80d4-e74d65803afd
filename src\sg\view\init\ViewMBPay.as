package sg.view.init {
    import ui.init.viewMBPayUI;
    import sg.model.ModelMB;
    import laya.events.Event;
    import sg.utils.Tools;
    import laya.display.Sprite;
    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;
    import sg.model.ModelUser;
    import sg.utils.ArrayUtil;
    import sg.net.NetHttp;
    import sg.model.ModelGame;
    import laya.utils.Handler;

    public class ViewMBPay extends viewMBPayUI {
        public var md:ModelMB;
        private var payArg:Array;
        private var httpStatus:Number = -1;
        private var salePid:Boolean = false;

        public function ViewMBPay() {
            this.mbPay.offAll();
            this.normalPay.offAll();
            this.mbPay.on(Event.CLICK, this, this.mbPayClick);
            this.normalPay.on(Event.CLICK, this, this.normalPayClick);
        }

        override public function initData():void {
            this.payArg = this.currArg;
            var payCfg:Object = ConfigServer.pay_config[this.payArg[0]];
            this.comTitle.setViewTitle(Tools.getMsgById("MB_title"));
            this.money.text = Tools.getMsgById("MB_money", [payCfg[0]]);
            this.info.text = Tools.getMsgById("MB_info");
            this.mbPay.label = Tools.getMsgById("MB_pay");
            this.normalPay.label = Tools.getMsgById("MB_common");
            // 显示item022道具数量而不是ucoin
            var item022Num:Number = ModelManager.instance.modelUser.property["item022"] || 0;
            this.mb_have.text = Tools.getMsgById("MB_have", [item022Num]);
        }

        private function mbPayClick():void {
            ModelGame.toPay(payArg[0], payArg[1], salePid, Handler.create(this, this.mbPayFunc));
            this.closeSelf();
        }

        //mb支付方法
        private function mbPayFunc():void {
            var pid:String = this.payArg[1] || payArg[0];
            var user:ModelUser = ModelManager.instance.modelUser;
            var ldt:Number = ConfigServer.getServerTimer();
            var orderId:String = pid + "|" + user.zone + "|" + user.mUID_base + "|" + ldt;
            var param:Object = {order_id: orderId,
                    sessionid: ModelManager.instance.modelUser.mSessionid};
            NetHttp.instance.send("user_zone.daijinquan_pay", param, Handler.create(this, this.mbPayCallback));
        }

        private function mbPayCallback(data:Object):void {
            this.httpStatus = Number(data["server_status"]);
            if (this.httpStatus == NetHttp.STATUS_SERVER_OK) {
                // 更新用户的item022道具数量，这里需要根据实际的用户道具数据结构来更新
                // 假设用户道具存储在ModelManager.instance.modelUser.prop中
                if (data["item022_num"] !== undefined) {
                    // 这里需要根据实际的前端道具数据结构来更新
                    // 可能需要调用相应的更新方法或直接设置属性
                    trace("代金券充值成功，剩余item022数量：" + data["item022_num"]);
                }
            }
        }

        private function normalPayClick():void {
            ModelGame.toPay(payArg[0], payArg[1], salePid);
            this.closeSelf();
        }

        override public function onRemoved():void {

        }

    }
}
