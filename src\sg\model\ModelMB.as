package sg.model {

    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;

    public class ModelMB extends ModelBase {
        public function isCanMBPay(pids:String):Boolean {
            var cfg:Object = ConfigServer.pay_config[pids];
            // 检查item022道具数量是否足够
            var item022Num:Number = ModelManager.instance.modelUser.property["item022"] || 0;
            if (item022Num >= cfg[0]) {
                return true;
            }
            return false;
        }
    }
}
